"""
Registration Validation Service

This service handles all validation logic for user registration including:
- Rate limiting validation
- Device validation
- Duplicate user checks
- Input data validation
"""

from typing import Dict, Any
from django.utils import timezone
from django.core.exceptions import ValidationError
from oauth2_auth.utils import (
    get_client_ip,
    get_dynamic_device_info,
)
from oauth2_auth.rate_limiting_service import rate_limiting_service
from oauth2_auth.authentication import DeviceAuthenticationService
from agritram.exceptions import (
    DuplicateResourceException,
    raise_validation_error,
)
from user.models import User
from user.password_validators import validate_fintech_password
import logging

logger = logging.getLogger(__name__)

# Import email domain validator


class RegistrationValidationService:
    """Service for handling all registration validation logic"""

    @staticmethod
    def validate_rate_limiting(request) -> None:
        """
        Validate rate limiting for registration attempts

        Args:
            request: HTTP request object
            unique_id: Unique request ID for correlation

        Raises:
            ValidationException: If rate limit is exceeded
        """
        client_ip = get_client_ip(request)
        rate_check_id = f"{client_ip}_registration"

        is_allowed, rate_info = rate_limiting_service.check_rate_limit(
            identifier=rate_check_id,
            action="registration",
            request=request,
        )

        if not is_allowed:
            raise_validation_error(
                message="Too many registration attempts",
                details=rate_info.get("message", "Please try again later"),
                error_code="RATE_LIMITED",
            )

    @classmethod
    def extract_and_validate_device_data(cls, request) -> Dict[str, Any]:
        """
        Extract and validate basic device data from request

        Args:
            request: HTTP request object
            unique_id: Unique request ID for correlation

        Returns:
            Dict containing extracted device information

        Raises:
            ValidationException: If basic device data validation fails
        """

        # Extract registration data
        email = request.data.get("email", "").lower().strip()

        # Extract device_id from multiple sources (headers take precedence)
        device_id = cls._extract_device_id_from_request(request)
        provided_device_type = request.data.get("device_type")
        user_provided_device_id = bool(device_id)

        # Generate device_id if not provided (for device tracking)
        if not device_id:
            # Use cryptographically secure random generation
            device_id = cls._generate_secure_device_id()

        # Validate device_id format and length for security
        cls._validate_device_id_format(device_id)

        # If device_id was provided by user, perform additional validation
        if user_provided_device_id:
            cls._validate_provided_device_id(device_id, provided_device_type, request)

        # Get dynamic device information
        device_info = get_dynamic_device_info(request, "registration")
        device_name = device_info["device_name"]
        detected_device_type = device_info["device_type"]

        # Validate provided device_type if given
        if provided_device_type:
            if provided_device_type not in ["web", "mobile", "desktop", "api"]:
                raise_validation_error(
                    message="Invalid device type",
                    details="Device type must be one of: web, mobile, desktop, api",
                )
            device_type = provided_device_type
        else:
            device_type = detected_device_type

        # Log device type mismatch if detected
        if provided_device_type and provided_device_type != detected_device_type:
            logger.warning(
                "DEVICE_TYPE_MISMATCH: Device type mismatch during registration: provided=%s, detected=%s",
                provided_device_type,
                detected_device_type,
                extra={
                    "email": email,
                    "device_id": device_id,
                    "provided_device_type": provided_device_type,
                    "detected_device_type": detected_device_type,
                    "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                    "operation_type": "DEVICE_TYPE_MISMATCH",
                },
            )

        return {
            "device_id": device_id,
            "device_name": device_name,
            "device_type": device_type,
            "detected_device_type": detected_device_type,
            "provided_device_type": provided_device_type,
            "user_provided_device_id": user_provided_device_id,
            "email": email,
        }

    @classmethod
    def _extract_device_id_from_request(cls, request) -> str:
        """
        Extract device_id from request headers or body

        Args:
            request: HTTP request object

        Returns:
            str: Device ID if found, empty string otherwise
        """
        # Check headers first (X-Device-ID)
        device_id = request.META.get("HTTP_X_DEVICE_ID", "").strip()

        # If not in headers, check request body
        if not device_id:
            device_id = request.data.get("device_id", "").strip()

        return device_id

    @classmethod
    def _generate_secure_device_id(cls) -> str:
        """
        Generate a cryptographically secure device ID

        Returns:
            str: Generated device ID with timestamp and secure token
        """
        import secrets

        return f"{timezone.now().strftime('%Y%m%d%H%M%S')}_{secrets.token_urlsafe(32)}"

    @classmethod
    def _validate_device_id_format(cls, device_id: str) -> None:
        """
        Validate device_id format and length

        Args:
            device_id: Device ID to validate

        Raises:
            ValidationException: If device_id format is invalid
        """
        if not device_id or len(device_id) < 58:
            raise_validation_error(
                message="Invalid device identifier",
                details="Device ID must be at least 58 characters long",
            )

    @classmethod
    def _validate_provided_device_id(
        cls, device_id: str, device_type: str, request
    ) -> None:
        """
        Validate user-provided device_id against database and device_type

        Args:
            device_id: Device ID to validate
            device_type: Expected device type
            request: HTTP request object

        Raises:
            ValidationException: If validation fails
        """
        from oauth2_auth.models import DeviceToken

        try:
            # Check if device exists in database
            existing_device = DeviceToken.objects.get(device_id=device_id)

            # Validate device_type match if provided
            if device_type and existing_device.device_type != device_type:
                logger.warning(
                    "DEVICE_TYPE_MISMATCH: Device type mismatch for existing device",
                    extra={
                        "device_id": device_id,
                        "stored_device_type": existing_device.device_type,
                        "provided_device_type": device_type,
                        "existing_user_email": existing_device.user.email,
                        "operation_type": "DEVICE_VALIDATION_MISMATCH",
                    },
                )
                raise_validation_error(
                    message="Device type mismatch",
                    details=f"Device is registered as {existing_device.device_type}, but {device_type} was provided",
                )

            # Additional security check: prevent device hijacking
            # Generate current fingerprint and compare with stored
            from oauth2_auth.utils import generate_device_fingerprint

            current_fingerprint = generate_device_fingerprint(request)

            if existing_device.fingerprint != current_fingerprint:
                logger.warning(
                    "DEVICE_FINGERPRINT_MISMATCH: Device fingerprint mismatch - possible spoofing attempt",
                    extra={
                        "device_id": device_id,
                        "stored_fingerprint": existing_device.fingerprint[:10] + "...",
                        "current_fingerprint": current_fingerprint[:10] + "...",
                        "existing_user_email": existing_device.user.email,
                        "operation_type": "DEVICE_FINGERPRINT_MISMATCH",
                    },
                )
                raise_validation_error(
                    message="Device verification failed",
                    details="Device fingerprint does not match registered device",
                )

        except DeviceToken.DoesNotExist:
            # Device doesn't exist - this is suspicious for a provided device_id
            logger.warning(
                "UNKNOWN_DEVICE_ID: User provided device_id that doesn't exist in database",
                extra={
                    "device_id": device_id,
                    "provided_device_type": device_type,
                    "operation_type": "UNKNOWN_DEVICE_REGISTRATION",
                },
            )
            raise_validation_error(
                message="Unknown device identifier",
                details="The provided device ID is not registered in the system",
            )

    @staticmethod
    def validate_device_against_database(device_data: Dict[str, Any], request) -> None:
        """
        Validate device against existing devices in database when user provides device_id

        Args:
            device_data: Device information dictionary
            request: HTTP request object
            unique_id: Unique request ID for correlation

        Raises:
            ValidationException: If device validation fails
        """
        # Only validate against DB if user explicitly provided device_id
        if device_data["user_provided_device_id"]:
            logger.info(
                "DEVICE_DB_VALIDATION_START: Starting database validation for user-provided device: %s",
                device_data["device_id"],
                extra={
                    "device_id": device_data["device_id"],
                    "email": device_data["email"],
                    "device_type": device_data["device_type"],
                    "operation_type": "DEVICE_DB_VALIDATION_START",
                },
            )

            validation_result = (
                DeviceAuthenticationService.validate_device_for_registration(
                    device_id=device_data["device_id"],
                    device_type=device_data["device_type"],
                    request=request,
                )
            )

            if not validation_result["is_valid"]:
                # Device validation failed - raise error
                logger.error(
                    "DEVICE_DB_VALIDATION_FAILED: Database validation failed for device: %s",
                    validation_result["message"],
                    extra={
                        "device_id": device_data["device_id"],
                        "email": device_data["email"],
                        "validation_message": validation_result["message"],
                        "operation_type": "DEVICE_DB_VALIDATION_FAILED",
                    },
                )

                raise_validation_error(
                    message="Device validation failed",
                    details=validation_result["message"],
                )

            # Log any warnings from validation
            if validation_result["warnings"]:
                logger.warning(
                    "DEVICE_DB_VALIDATION_WARNINGS: Database validation warnings for registration: %s",
                    ", ".join(validation_result["warnings"]),
                    extra={
                        "email": device_data["email"],
                        "device_id": device_data["device_id"],
                        "warnings": validation_result["warnings"],
                        "validation_details": validation_result["details"],
                        "operation_type": "DEVICE_DB_VALIDATION_WARNINGS",
                    },
                )

            logger.info(
                "DEVICE_DB_VALIDATION_SUCCESS: Database validation successful for device: %s",
                device_data["device_id"],
                extra={
                    "device_id": device_data["device_id"],
                    "email": device_data["email"],
                    "validation_details": validation_result.get("details", {}),
                    "operation_type": "DEVICE_DB_VALIDATION_SUCCESS",
                },
            )
        else:
            logger.info(
                "DEVICE_DB_VALIDATION_SKIPPED: Skipping database validation for generated device: %s",
                device_data["device_id"],
                extra={
                    "device_id": device_data["device_id"],
                    "email": device_data["email"],
                    "reason": "device_id_generated_by_system",
                    "operation_type": "DEVICE_DB_VALIDATION_SKIPPED",
                },
            )

        # Log comprehensive device validation summary
        logger.info(
            "DEVICE_VALIDATION_COMPLETE: Device validation completed for registration: %s",
            device_data["device_id"],
            extra={
                "device_id": device_data["device_id"],
                "email": device_data["email"],
                "device_name": device_data["device_name"],
                "device_type": device_data["device_type"],
                "detected_device_type": device_data["detected_device_type"],
                "provided_device_type": device_data["provided_device_type"],
                "user_provided_device_id": device_data["user_provided_device_id"],
                "db_validation_performed": device_data["user_provided_device_id"],
                "validation_passed": True,
                "operation_type": "DEVICE_VALIDATION_COMPLETE",
            },
        )

    @staticmethod
    def validate_duplicate_user(email: str, request) -> None:
        """
        Check for duplicate registration attempts

        Args:
            email: User email to check
            unique_id: Unique request ID for correlation
            request: HTTP request object

        Raises:
            DuplicateResourceException: If user already exists
        """
        if email:
            try:
                existing_user = User.objects.get(email=email)
                if existing_user:
                    # Log security event with standardized logging
                    # Assuming log_security_event_standardized is defined elsewhere or will be added.
                    # For now, using a placeholder or removing if not defined.
                    # log_security_event_standardized(
                    #     "DUPLICATE_REGISTRATION_ATTEMPT",
                    #     "Registration attempt with existing active email",
                    #     user=existing_user,
                    #     request=request,
                    #     metadata={"email": email},
                    #     level="WARNING",
                    # )
                    logger.warning(
                        "DUPLICATE_REGISTRATION_ATTEMPT: Registration attempt with existing active email: %s",
                        email,
                        extra={
                            "email": email,
                            "existing_user_id": existing_user.id,
                            "operation_type": "DUPLICATE_REGISTRATION_ATTEMPT",
                        },
                    )
                    raise DuplicateResourceException(
                        message="User with this email already exists",
                        details="An active account with this email address is already registered",
                    )
            except User.DoesNotExist:
                pass

    @classmethod
    def validate_email_domain(cls, email: str) -> None:
        """
        Validate email domain to prevent disposable emails and ensure deliverability

        Args:
            email: Email address to validate

        Raises:
            ValidationException: If email domain is invalid
        """
        from user.email_domain_validator import email_domain_validator

        try:
            is_valid, error_message, validation_details = (
                email_domain_validator.validate_email_domain(email)
            )

            if not is_valid:
                logger.warning(
                    "Email domain validation failed",
                    extra={
                        "event_type": "EMAIL_DOMAIN_VALIDATION_FAILURE",
                        "email": email,
                        "domain": validation_details.get("domain"),
                        "is_disposable": validation_details.get("is_disposable", False),
                        "has_mx_record": validation_details.get("has_mx_record", False),
                        "error_message": error_message,
                        "operation_type": "REGISTRATION_EMAIL_VALIDATION",
                    },
                )

                # Provide specific error messages based on validation failure
                if validation_details.get("is_disposable"):
                    raise_validation_error(
                        message="Disposable email addresses are not allowed",
                        details="Please use a permanent email address from a legitimate email provider. Temporary or disposable email services are not permitted for account registration.",
                    )
                elif not validation_details.get("has_mx_record"):
                    raise_validation_error(
                        message="Email domain cannot receive emails",
                        details="The email domain you provided does not appear to be configured to receive emails. Please check your email address or use a different email provider.",
                    )
                else:
                    raise_validation_error(
                        message="Invalid email domain",
                        details=error_message
                        or "The email domain you provided is not valid for registration.",
                    )

            logger.info(
                "Email domain validation successful",
                extra={
                    "event_type": "EMAIL_DOMAIN_VALIDATION_SUCCESS",
                    "email": email,
                    "domain": validation_details.get("domain"),
                    "mx_count": len(validation_details.get("mx_records", [])),
                    "operation_type": "REGISTRATION_EMAIL_VALIDATION",
                },
            )

        except Exception as e:
            # If it's already a validation error, re-raise it
            if "ValidationException" in str(type(e)) or hasattr(e, "message"):
                raise

            # For unexpected errors, log and allow registration (fail open)
            logger.error(
                f"Unexpected error during email domain validation: {str(e)}",
                extra={
                    "event_type": "EMAIL_DOMAIN_VALIDATION_ERROR",
                    "email": email,
                    "error": str(e),
                    "error_type": e.__class__.__name__,
                    "operation_type": "REGISTRATION_EMAIL_VALIDATION",
                },
                exc_info=True,
            )
            # Don't block registration for unexpected validation errors

    @classmethod
    def validate_password_strength(cls, request, email: str) -> None:
        """
        Validate password strength using fintech-grade requirements

        Args:
            request: HTTP request object
            email: User email for similarity checking

        Raises:
            ValidationException: If password doesn't meet requirements
        """
        password = request.data.get("password")

        if not password:
            raise_validation_error(
                message="Password is required",
                details="Password field cannot be empty",
            )

        # Create a temporary user object for similarity checking
        class TempUser:
            def __init__(self, email):
                self.email = email
                self.name = request.data.get("name", "")
                # Split name into first/last if provided
                name_parts = self.name.split() if self.name else []
                self.first_name = name_parts[0] if name_parts else ""
                self.last_name = " ".join(name_parts[1:]) if len(name_parts) > 1 else ""

        temp_user = TempUser(email)

        try:
            validate_fintech_password(password, temp_user)

            logger.info(
                "Password validation successful for registration",
                extra={
                    "event_type": "REGISTRATION_PASSWORD_VALIDATION_SUCCESS",
                    "email": email,
                    "password_length": len(password),
                    "operation_type": "REGISTRATION_PASSWORD_VALIDATION",
                },
            )

        except ValidationError as e:
            # Convert Django ValidationError to our custom exception
            error_messages = e.messages if hasattr(e, "messages") else [str(e)]

            logger.warning(
                "Password validation failed for registration",
                extra={
                    "event_type": "REGISTRATION_PASSWORD_VALIDATION_FAILURE",
                    "email": email,
                    "password_length": len(password) if password else 0,
                    "validation_errors": error_messages,
                    "operation_type": "REGISTRATION_PASSWORD_VALIDATION",
                },
            )

            raise_validation_error(
                message="Password does not meet security requirements",
                details="; ".join(error_messages),
            )

    @classmethod
    def validate_registration_request(cls, request) -> Dict[str, Any]:
        """
        Perform complete validation of registration request

        Args:
            request: HTTP request object

        Returns:
            Dict containing validated data

        Raises:
            ValidationException: If any validation fails
            DuplicateResourceException: If user already exists
        """
        # Step 1: Validate rate limiting
        cls.validate_rate_limiting(request)

        # Step 2: Extract and validate basic device data
        device_data = cls.extract_and_validate_device_data(request)

        # Step 3: Validate device against database if user provided device_id
        cls.validate_device_against_database(device_data, request)

        # Step 4: Validate email domain
        cls.validate_email_domain(device_data["email"])

        # Step 5: Validate password strength
        cls.validate_password_strength(request, device_data["email"])

        # Step 6: Check for duplicate users
        cls.validate_duplicate_user(device_data["email"], request)

        return device_data
