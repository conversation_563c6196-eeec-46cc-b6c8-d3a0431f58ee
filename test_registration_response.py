#!/usr/bin/env python3
"""
Test script to verify the refactored registration response behavior.

This script tests:
1. Default response (should only contain success object)
2. Extended response (should contain success + minimal data)
3. Debug response (should contain success + full data)
"""

import requests
import json
import uuid
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api"
REGISTRATION_ENDPOINT = f"{BASE_URL}/user/register/"

def generate_test_data():
    """Generate unique test data for registration"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    
    return {
        "name": f"Test User {timestamp}",
        "email": f"test_{timestamp}_{unique_id}@example.com",
        "password": "TestPassword123!",
        "device_id": f"test_device_{timestamp}_{unique_id}",
        "device_name": f"Test Device {timestamp}",
        "device_fingerprint": f"test_fingerprint_{unique_id}",
        "device_info": {
            "user_agent": "Test User Agent",
            "platform": "test",
            "language": "en",
            "timezone": "UTC",
            "screen_resolution": "1920x1080"
        }
    }

def test_registration_response(query_params=None, test_name="Default"):
    """Test registration with optional query parameters"""
    print(f"\n{'='*50}")
    print(f"Testing {test_name} Registration Response")
    print(f"{'='*50}")
    
    # Generate unique test data
    data = generate_test_data()
    
    # Build URL with query parameters
    url = REGISTRATION_ENDPOINT
    if query_params:
        params = "&".join([f"{k}={v}" for k, v in query_params.items()])
        url = f"{url}?{params}"
    
    print(f"URL: {url}")
    print(f"Email: {data['email']}")
    
    try:
        response = requests.post(url, json=data, headers={"Content-Type": "application/json"})
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            response_json = response.json()
            print(f"Response JSON:")
            print(json.dumps(response_json, indent=2))
            
            # Analyze response structure
            has_success = "success" in response_json
            has_data = "data" in response_json
            
            print(f"\nResponse Analysis:")
            print(f"- Has 'success' object: {has_success}")
            print(f"- Has 'data' object: {has_data}")
            
            if has_success:
                success_obj = response_json["success"]
                print(f"- Success code: {success_obj.get('code')}")
                print(f"- Success message: {success_obj.get('message')}")
            
            if has_data:
                data_obj = response_json["data"]
                print(f"- Data keys: {list(data_obj.keys())}")
                if "device" in data_obj:
                    print(f"- Device data: {data_obj['device']}")
            
            return True, response_json
            
        else:
            print(f"Error Response:")
            try:
                error_json = response.json()
                print(json.dumps(error_json, indent=2))
            except:
                print(response.text)
            return False, None
            
    except Exception as e:
        print(f"Request failed: {str(e)}")
        return False, None

def main():
    """Run all registration response tests"""
    print("Registration Response Refactoring Test")
    print("=" * 60)
    
    # Test 1: Default response (should only have success object)
    success1, response1 = test_registration_response(
        test_name="Default (No Query Params)"
    )
    
    # Test 2: Extended response (should have success + minimal data)
    success2, response2 = test_registration_response(
        query_params={"extended": "true"},
        test_name="Extended Mode"
    )
    
    # Test 3: Debug response (should have success + full data)
    success3, response3 = test_registration_response(
        query_params={"debug": "true"},
        test_name="Debug Mode"
    )
    
    # Test 4: Both debug and extended
    success4, response4 = test_registration_response(
        query_params={"debug": "true", "extended": "true"},
        test_name="Debug + Extended Mode"
    )
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Default response: {'✓ PASS' if success1 else '✗ FAIL'}")
    print(f"Extended response: {'✓ PASS' if success2 else '✗ FAIL'}")
    print(f"Debug response: {'✓ PASS' if success3 else '✗ FAIL'}")
    print(f"Debug + Extended response: {'✓ PASS' if success4 else '✗ FAIL'}")
    
    # Validate expected behavior
    print(f"\nEXPECTED BEHAVIOR VALIDATION:")
    
    if success1 and response1:
        has_data_default = "data" in response1
        print(f"Default response has no data: {'✓ PASS' if not has_data_default else '✗ FAIL'}")
    
    if success2 and response2:
        has_data_extended = "data" in response2
        has_device_id = has_data_extended and "device" in response2.get("data", {})
        print(f"Extended response has device data: {'✓ PASS' if has_device_id else '✗ FAIL'}")
    
    if success3 and response3:
        has_data_debug = "data" in response3
        has_user_data = has_data_debug and "user" in response3.get("data", {})
        print(f"Debug response has user data: {'✓ PASS' if has_user_data else '✗ FAIL'}")

if __name__ == "__main__":
    main()
