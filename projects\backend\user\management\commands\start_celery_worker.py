"""
Django management command to start Celery worker

Usage:
    python manage.py start_celery_worker
    python manage.py start_celery_worker --queue=email
    python manage.py start_celery_worker --concurrency=4
"""

import os
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Start Celery worker for email processing"

    def add_arguments(self, parser):
        parser.add_argument(
            "--queue",
            type=str,
            default="email",
            help="Queue name to process (default: email)",
        )
        parser.add_argument(
            "--concurrency",
            type=int,
            default=2,
            help="Number of concurrent worker processes (default: 2)",
        )
        parser.add_argument(
            "--loglevel",
            type=str,
            default="info",
            choices=["debug", "info", "warning", "error", "critical"],
            help="Logging level (default: info)",
        )
        parser.add_argument(
            "--beat", action="store_true", help="Also start Celery beat scheduler"
        )

    def handle(self, *args, **options):
        queue = options["queue"]
        concurrency = options["concurrency"]
        loglevel = options["loglevel"]
        start_beat = options["beat"]

        self.stdout.write(
            self.style.SUCCESS(f"Starting Celery worker for queue: {queue}")
        )
        self.stdout.write(f"Concurrency: {concurrency}")
        self.stdout.write(f"Log level: {loglevel}")

        if start_beat:
            self.stdout.write("Beat scheduler: enabled")

        # Build Celery command
        cmd_parts = [
            "celery",
            "-A",
            "agritram",
            "worker",
            "--queues",
            queue,
            "--concurrency",
            str(concurrency),
            "--loglevel",
            loglevel,
        ]

        if start_beat:
            cmd_parts.extend(["--beat"])

        # Add additional worker options for production
        cmd_parts.extend(
            [
                "--time-limit=300",  # 5 minutes hard time limit
                "--soft-time-limit=240",  # 4 minutes soft time limit
                "--max-tasks-per-child=1000",  # Restart worker after 1000 tasks
                "--prefetch-multiplier=1",  # Disable prefetching for fair distribution
            ]
        )

        cmd = " ".join(cmd_parts)

        self.stdout.write(f"Command: {cmd}")
        self.stdout.write(self.style.WARNING("Press Ctrl+C to stop the worker"))

        # Execute the command
        try:
            os.system(cmd)
        except KeyboardInterrupt:
            self.stdout.write(self.style.SUCCESS("\nCelery worker stopped"))
