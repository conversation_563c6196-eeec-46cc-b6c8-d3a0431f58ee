"""
Celery Configuration for Agritram

This module configures Celery for asynchronous task processing including:
- Email delivery tasks
- Background processing
- Retry mechanisms with exponential backoff
- Redis broker configuration
"""

import os
from time import timezone
from celery import Celery
from django.conf import settings
from decouple import config

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')

# Create Celery app
app = Celery('agritram')

# Configure Celery using Django settings
app.config_from_object('django.conf:settings', namespace='CELERY')

# Celery Configuration
app.conf.update(
    # Broker settings
    broker_url=config('CELERY_BROKER_URL', default='redis://localhost:6379/0'),
    result_backend=config('CELERY_RESULT_BACKEND', default='redis://localhost:6379/0'),
    
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Task routing
    task_routes={
        'user.tasks.send_activation_email_task': {'queue': 'email'},
        'user.tasks.send_welcome_email_task': {'queue': 'email'},
        'user.tasks.send_password_reset_email_task': {'queue': 'email'},
        'user.tasks.send_security_alert_email_task': {'queue': 'email'},
        'user.tasks.send_bulk_notification_task': {'queue': 'email'},
    },
    
    # Worker settings
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=False,
    
    # Retry settings
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Result settings
    result_expires=3600,  # 1 hour
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Security
    worker_hijack_root_logger=False,
    worker_log_color=False,
    
    # Email queue specific settings
    task_annotations={
        'user.tasks.send_activation_email_task': {
            'rate_limit': '100/m',  # 100 emails per minute
            'retry_backoff': True,
            'retry_backoff_max': 700,  # Max 700 seconds
            'retry_jitter': False,
        },
        'user.tasks.send_welcome_email_task': {
            'rate_limit': '100/m',
            'retry_backoff': True,
            'retry_backoff_max': 700,
        },
        'user.tasks.send_password_reset_email_task': {
            'rate_limit': '50/m',  # Lower rate for security emails
            'retry_backoff': True,
            'retry_backoff_max': 700,
        },
        'user.tasks.send_security_alert_email_task': {
            'rate_limit': '50/m',
            'retry_backoff': True,
            'retry_backoff_max': 700,
        },
        'user.tasks.send_bulk_notification_task': {
            'rate_limit': '10/m',  # Lower rate for bulk emails
            'retry_backoff': True,
            'retry_backoff_max': 1800,  # Max 30 minutes for bulk
        },
    },
    
    # Beat scheduler settings (for periodic tasks)
    beat_schedule={
        'cleanup-failed-email-tasks': {
            'task': 'user.tasks.cleanup_failed_email_tasks',
            'schedule': 3600.0,  # Every hour
        },
    },
)

# Auto-discover tasks from all registered Django apps
app.autodiscover_tasks()

# Custom task base class for better error handling
from celery import Task
import logging

logger = logging.getLogger(__name__)

class CallbackTask(Task):
    """
    Custom task base class with enhanced error handling and logging
    """
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called when task succeeds"""
        logger.info(
            f"Task {self.name} succeeded",
            extra={
                'task_id': task_id,
                'task_name': self.name,
                'args': args,
                'kwargs': kwargs,
                'result': retval,
                'event_type': 'CELERY_TASK_SUCCESS'
            }
        )
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called when task fails"""
        logger.error(
            f"Task {self.name} failed: {exc}",
            extra={
                'task_id': task_id,
                'task_name': self.name,
                'args': args,
                'kwargs': kwargs,
                'exception': str(exc),
                'traceback': str(einfo),
                'event_type': 'CELERY_TASK_FAILURE'
            },
            exc_info=True
        )
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Called when task is retried"""
        logger.warning(
            f"Task {self.name} retrying: {exc}",
            extra={
                'task_id': task_id,
                'task_name': self.name,
                'args': args,
                'kwargs': kwargs,
                'exception': str(exc),
                'retry_count': self.request.retries,
                'max_retries': self.max_retries,
                'event_type': 'CELERY_TASK_RETRY'
            }
        )

# Set the custom task base class
app.Task = CallbackTask

@app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup"""
    print(f'Request: {self.request!r}')
    return 'Celery is working!'


# Health check task
@app.task(bind=True)
def health_check(self):
    """Health check task for monitoring"""
    try:
        # Test Redis connection
        from django.core.cache import cache
        cache.set('celery_health_check', 'ok', 30)
        result = cache.get('celery_health_check')
        
        if result == 'ok':
            logger.info(
                "Celery health check passed",
                extra={
                    'task_id': self.request.id,
                    'event_type': 'CELERY_HEALTH_CHECK_SUCCESS'
                }
            )
            return {'status': 'healthy', 'timestamp': str(timezone.now())}
        else:
            raise Exception("Cache test failed")
            
    except Exception as e:
        logger.error(
            f"Celery health check failed: {e}",
            extra={
                'task_id': self.request.id,
                'error': str(e),
                'event_type': 'CELERY_HEALTH_CHECK_FAILURE'
            },
            exc_info=True
        )
        raise self.retry(exc=e, countdown=60, max_retries=3)


# Email queue monitoring task
@app.task(bind=True)
def monitor_email_queue(self):
    """Monitor email queue health and performance"""
    try:
        from celery import current_app
        
        # Get queue statistics
        inspect = current_app.control.inspect()
        stats = inspect.stats()
        active_tasks = inspect.active()
        
        logger.info(
            "Email queue monitoring",
            extra={
                'task_id': self.request.id,
                'queue_stats': stats,
                'active_tasks_count': len(active_tasks) if active_tasks else 0,
                'event_type': 'CELERY_QUEUE_MONITORING'
            }
        )
        
        return {
            'status': 'monitored',
            'stats': stats,
            'active_tasks_count': len(active_tasks) if active_tasks else 0
        }
        
    except Exception as e:
        logger.error(
            f"Email queue monitoring failed: {e}",
            extra={
                'task_id': self.request.id,
                'error': str(e),
                'event_type': 'CELERY_QUEUE_MONITORING_FAILURE'
            },
            exc_info=True
        )
        return {'status': 'error', 'error': str(e)}


if __name__ == '__main__':
    app.start()
