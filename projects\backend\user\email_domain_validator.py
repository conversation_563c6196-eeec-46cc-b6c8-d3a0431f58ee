"""
Email Domain Validation Service

This service provides comprehensive email domain validation including:
- Disposable email detection
- MX record verification
- Domain reputation checking
- Bot and spam prevention
"""

import logging
from typing import Dict, List, Optional, Tuple
from django.core.cache import cache
import dns.resolver
import dns.exception

logger = logging.getLogger(__name__)


class EmailDomainValidator:
    """
    Comprehensive email domain validator for preventing spam and bot registrations
    """

    # Comprehensive list of known disposable email domains
    DISPOSABLE_DOMAINS = {
        # Popular disposable email services
        "10minutemail.com",
        "10minutemail.net",
        "10minutemail.org",
        "20minutemail.com",
        "2prong.com",
        "30minutemail.com",
        "33mail.com",
        "3d-painting.com",
        "4warding.com",
        "7tags.com",
        "9ox.net",
        "aaathats3as.com",
        "agedmail.com",
        "amilegit.com",
        "anonbox.net",
        "anonymbox.com",
        "antichef.com",
        "antichef.net",
        "antispam.de",
        "bccto.me",
        "beefmilk.com",
        "bigstring.com",
        "binkmail.com",
        "bio-muesli.net",
        "bobmail.info",
        "bodhi.lawlita.com",
        "bofthew.com",
        "breakthru.com",
        "brefmail.com",
        "broadbandninja.com",
        "bsnow.net",
        "bugmenot.com",
        "bumpymail.com",
        "burnthismail.com",
        "burstmail.info",
        "buyusedlibrarybooks.org",
        "byom.de",
        "c2.hu",
        "card.zp.ua",
        "casualdx.com",
        "cek.pm",
        "centermail.com",
        "centermail.net",
        "chammy.info",
        "childsavetrust.org",
        "chogmail.com",
        "choicemail1.com",
        "clixser.com",
        "cmail.net",
        "cmail.org",
        "coldemail.info",
        "cool.fr.nf",
        "correo.blogos.net",
        "cosmorph.com",
        "courriel.fr.nf",
        "courrieltemporaire.com",
        "crankmail.com",
        "crazymailing.com",
        "cubiclink.com",
        "curryworld.de",
        "cust.in",
        "dacoolest.com",
        "dandikmail.com",
        "dayrep.com",
        "dcemail.com",
        "deadaddress.com",
        "deadspam.com",
        "delikkt.com",
        "despam.it",
        "despammed.com",
        "devnullmail.com",
        "dfgh.net",
        "digitalsanctuary.com",
        "dingbone.com",
        "discard.email",
        "discardmail.com",
        "discardmail.de",
        "disposableaddress.com",
        "disposableemailaddresses.com",
        "disposableinbox.com",
        "dispose.it",
        "disposeamail.com",
        "disposemail.com",
        "dispostable.com",
        "dm.w3internet.com",
        "dodgeit.com",
        "dodgit.com",
        "dodgit.org",
        "donemail.ru",
        "dontreg.com",
        "dontsendmespam.de",
        "drdrb.net",
        "dump-email.info",
        "dumpandjunk.com",
        "dumpmail.de",
        "dumpyemail.com",
        "e-mail.com",
        "e-mail.org",
        "e4ward.com",
        "easytrashmail.com",
        "einrot.com",
        "email60.com",
        "emaildienst.de",
        "emailgo.de",
        "emailias.com",
        "emailinfive.com",
        "emailmiser.com",
        "emailsensei.com",
        "emailtemporanea.com",
        "emailtemporanea.net",
        "emailtemporar.ro",
        "emailtemporario.com.br",
        "emailthe.net",
        "emailtmp.com",
        "emailto.de",
        "emailwarden.com",
        "emailx.at.hm",
        "emailxfer.com",
        "emeil.in",
        "emeil.ir",
        "emz.net",
        "enterto.com",
        "ephemail.net",
        "etranquil.com",
        "etranquil.net",
        "etranquil.org",
        "evopo.com",
        "explodemail.com",
        "express.net.ua",
        "eyepaste.com",
        "fakeinbox.com",
        "fakemailz.com",
        "fakemail.fr",
        "fansworldwide.de",
        "fantasymail.de",
        "fastacura.com",
        "fastchevy.com",
        "fastchrysler.com",
        "fastkawasaki.com",
        "fastmazda.com",
        "fastmitsubishi.com",
        "fastnissan.com",
        "fastsubaru.com",
        "fastsuzuki.com",
        "fasttoyota.com",
        "fastyamaha.com",
        "filzmail.com",
        "fizmail.com",
        "fleckens.hu",
        "flyspam.com",
        "footard.com",
        "forgetmail.com",
        "fr33mail.info",
        "frapmail.com",
        "freemails.cf",
        "freemails.ga",
        "freemails.ml",
        "freundin.ru",
        "friendlymail.co.uk",
        "front14.org",
        "fudgerub.com",
        "fux0ringduh.com",
        "garliclife.com",
        "get-mail.cf",
        "get-mail.ga",
        "get-mail.ml",
        "get-mail.tk",
        "get1mail.com",
        "get2mail.fr",
        "getairmail.com",
        "getmails.eu",
        "getonemail.com",
        "getonemail.net",
        "ghosttexter.de",
        "giantmail.de",
        "girlsundertheinfluence.com",
        "gishpuppy.com",
        "gmial.com",
        "goemailgo.com",
        "gotmail.com",
        "gotmail.net",
        "gotmail.org",
        "gotti.otherinbox.com",
        "great-host.in",
        "greensloth.com",
        "grr.la",
        "gsrv.co.uk",
        "guerillamail.biz",
        "guerillamail.com",
        "guerillamail.de",
        "guerillamail.info",
        "guerillamail.net",
        "guerillamail.org",
        "guerrillamail.biz",
        "guerrillamail.com",
        "guerrillamail.de",
        "guerrillamail.info",
        "guerrillamail.net",
        "guerrillamail.org",
        "guerrillamailblock.com",
        "gustr.com",
        "harakirimail.com",
        "hatespam.org",
        "herp.in",
        "hidemail.de",
        "hidzz.com",
        "hmamail.com",
        "hopemail.biz",
        "hotpop.com",
        "hulapla.de",
        "ieatspam.eu",
        "ieatspam.info",
        "ieh-mail.de",
        "ikbenspamvrij.nl",
        "imails.info",
        "inboxalias.com",
        "inboxclean.com",
        "inboxclean.org",
        "incognitomail.com",
        "incognitomail.net",
        "incognitomail.org",
        "insorg-mail.info",
        "instant-mail.de",
        "ip6.li",
        "irish2me.com",
        "iwi.net",
        "jetable.com",
        "jetable.fr.nf",
        "jetable.net",
        "jetable.org",
        "jnxjn.com",
        "jourrapide.com",
        "jsrsolutions.com",
        "junk1e.com",
        "kaspop.com",
        "keepmymail.com",
        "killmail.com",
        "killmail.net",
        "klassmaster.com",
        "klzlk.com",
        "kook.ml",
        "koszmail.pl",
        "kurzepost.de",
        "lawlita.com",
        "letthemeatspam.com",
        "lhsdv.com",
        "lifebyfood.com",
        "link2mail.net",
        "litedrop.com",
        "lol.ovpn.to",
        "lolfreak.net",
        "lookugly.com",
        "lopl.co.cc",
        "lortemail.dk",
        "lr78.com",
        "lroid.com",
        "lukop.dk",
        "m4ilweb.info",
        "maboard.com",
        "mail-filter.com",
        "mail-temporaire.fr",
        "mail.by",
        "mail.mezimages.net",
        "mail.zp.ua",
        "mail1a.de",
        "mail21.cc",
        "mail2rss.org",
        "mail333.com",
        "mail4trash.com",
        "mailbidon.com",
        "mailbiz.biz",
        "mailblocks.com",
        "mailbucket.org",
        "mailcat.biz",
        "mailcatch.com",
        "mailde.de",
        "mailde.info",
        "maildrop.cc",
        "maildrop.cf",
        "maildrop.ga",
        "maildrop.gq",
        "maildrop.ml",
        "maileater.com",
        "mailed.ro",
        "mailexpire.com",
        "mailfa.tk",
        "mailforspam.com",
        "mailfreeonline.com",
        "mailguard.me",
        "mailimate.com",
        "mailin8r.com",
        "mailinater.com",
        "mailinator.com",
        "mailinator.net",
        "mailinator.org",
        "mailinator2.com",
        "mailincubator.com",
        "mailismagic.com",
        "mailme.lv",
        "mailme24.com",
        "mailmetrash.com",
        "mailmoat.com",
        "mailnator.com",
        "mailnesia.com",
        "mailnull.com",
        "mailorg.org",
        "mailpick.biz",
        "mailrock.biz",
        "mailscrap.com",
        "mailshell.com",
        "mailsiphon.com",
        "mailtemp.info",
        "mailtome.de",
        "mailtothis.com",
        "mailtrash.net",
        "mailtv.net",
        "mailtv.tv",
        "mailzilla.com",
        "mailzilla.org",
        "makemetheking.com",
        "manybrain.com",
        "mbx.cc",
        "mciek.com",
        "mega.zik.dj",
        "meinspamschutz.de",
        "meltmail.com",
        "messagebeamer.de",
        "mezimages.net",
        "mierdamail.com",
        "migmail.pl",
        "mintemail.com",
        "mjukglass.nu",
        "mobi.web.id",
        "moburl.com",
        "moncourrier.fr.nf",
        "monemail.fr.nf",
        "monmail.fr.nf",
        "monumentmail.com",
        "mt2009.com",
        "mt2014.com",
        "mycard.net.ua",
        "mycleaninbox.net",
        "myemailboxy.com",
        "mymail-in.net",
        "mymailoasis.com",
        "mynetstore.de",
        "mypacks.net",
        "mypartyclip.de",
        "myphantomemail.com",
        "myspaceinc.com",
        "myspaceinc.net",
        "myspaceinc.org",
        "myspacepimpedup.com",
        "myspamless.com",
        "mytrashmail.com",
        "nabuma.com",
        "neomailbox.com",
        "nepwk.com",
        "nervmich.net",
        "nervtmich.net",
        "netmails.com",
        "netmails.net",
        "netzidiot.de",
        "neverbox.com",
        "no-spam.ws",
        "nobulk.com",
        "noclickemail.com",
        "nogmailspam.info",
        "nomail.xl.cx",
        "nomail2me.com",
        "nomorespamemails.com",
        "nonspam.eu",
        "nonspammer.de",
        "noref.in",
        "nospam.ze.tc",
        "nospam4.us",
        "nospamfor.us",
        "nospammail.net",
        "nospamthanks.info",
        "notmailinator.com",
        "notsharingmy.info",
        "nowhere.org",
        "nowmymail.com",
        "nurfuerspam.de",
        "nus.edu.sg",
        "nwldx.com",
        "objectmail.com",
        "obobbo.com",
        "odnorazovoe.ru",
        "oneoffemail.com",
        "onewaymail.com",
        "onlatedotcom.info",
        "online.ms",
        "oopi.org",
        "opayq.com",
        "ordinaryamerican.net",
        "otherinbox.com",
        "ovpn.to",
        "owlpic.com",
        "pancakemail.com",
        "pjkh.com",
        "plexolan.de",
        "poczta.onet.pl",
        "politikerclub.de",
        "pooae.com",
        "pookmail.com",
        "privacy.net",
        "proxymail.eu",
        "prtnx.com",
        "punkass.com",
        "putthisinyourspamdatabase.com",
        "pwrby.com",
        "quickinbox.com",
        "rcpt.at",
        "reallymymail.com",
        "realtyalerts.ca",
        "recode.me",
        "recursor.net",
        "regbypass.com",
        "regbypass.comsafe-mail.net",
        "rejectmail.com",
        "reliable-mail.com",
        "rhyta.com",
        "rmqkr.net",
        "royal.net",
        "rppkn.com",
        "rtrtr.com",
        "rumgel.com",
        "s0ny.net",
        "safe-mail.net",
        "safersignup.de",
        "safetymail.info",
        "safetypost.de",
        "sandelf.de",
        "saynotospams.com",
        "schafmail.de",
        "schrott-email.de",
        "secretemail.de",
        "secure-mail.biz",
        "selfdestructingmail.com",
        "selfdestructingmail.org",
        "sendspamhere.de",
        "sharklasers.com",
        "shieldedmail.com",
        "shiftmail.com",
        "shitmail.me",
        "shitware.nl",
        "shmeriously.com",
        "shortmail.net",
        "sibmail.com",
        "skeefmail.com",
        "slaskpost.se",
        "slopsbox.com",
        "smellfear.com",
        "snakemail.com",
        "sneakemail.com",
        "snkmail.com",
        "sofimail.com",
        "sofort-mail.de",
        "sogetthis.com",
        "soodonims.com",
        "spam.la",
        "spam.su",
        "spam4.me",
        "spamail.de",
        "spambob.com",
        "spambob.net",
        "spambob.org",
        "spambog.com",
        "spambog.de",
        "spambog.ru",
        "spambox.info",
        "spambox.irishspringtours.com",
        "spambox.us",
        "spamcannon.com",
        "spamcannon.net",
        "spamcero.com",
        "spamcon.org",
        "spamcorptastic.com",
        "spamcowboy.com",
        "spamcowboy.net",
        "spamcowboy.org",
        "spamday.com",
        "spamex.com",
        "spamfree24.com",
        "spamfree24.de",
        "spamfree24.eu",
        "spamfree24.net",
        "spamfree24.org",
        "spamgoes.com",
        "spamgourmet.com",
        "spamgourmet.net",
        "spamgourmet.org",
        "spamhole.com",
        "spamify.com",
        "spaminator.de",
        "spamkill.info",
        "spaml.com",
        "spaml.de",
        "spammotel.com",
        "spamobox.com",
        "spamoff.de",
        "spamslicer.com",
        "spamspot.com",
        "spamstack.net",
        "spamthis.co.uk",
        "spamthisplease.com",
        "spamtrail.com",
        "spamtroll.net",
        "speed.1s.fr",
        "spoofmail.de",
        "stuffmail.de",
        "super-auswahl.de",
        "supergreatmail.com",
        "supermailer.jp",
        "superrito.com",
        "superstachel.de",
        "suremail.info",
        "talkinator.com",
        "teewars.org",
        "teleworm.com",
        "teleworm.us",
        "temp-mail.org",
        "temp-mail.ru",
        "tempalias.com",
        "tempe-mail.com",
        "tempemail.biz",
        "tempemail.com",
        "tempinbox.co.uk",
        "tempinbox.com",
        "tempmail.eu",
        "tempmail2.com",
        "tempmaildemo.com",
        "tempmailer.com",
        "tempmailer.de",
        "tempmail.org",
        "tempomail.fr",
        "temporarily.de",
        "temporarioemail.com.br",
        "temporaryemail.net",
        "temporaryforwarding.com",
        "temporaryinbox.com",
        "temporarymailaddress.com",
        "tempthe.net",
        "thanksnospam.info",
        "thankyou2010.com",
        "thecloudindex.com",
        "thisisnotmyrealemail.com",
        "thismail.net",
        "throwawayemailaddresses.com",
        "tilien.com",
        "tittbit.in",
        "tmail.ws",
        "tmailinator.com",
        "toiea.com",
        "toomail.biz",
        "topranklist.de",
        "tradermail.info",
        "trash-amil.com",
        "trash-mail.at",
        "trash-mail.com",
        "trash-mail.de",
        "trash2009.com",
        "trashdevil.com",
        "trashdevil.de",
        "trashemail.de",
        "trashmail.at",
        "trashmail.com",
        "trashmail.de",
        "trashmail.me",
        "trashmail.net",
        "trashmail.org",
        "trashmail.ws",
        "trashmailer.com",
        "trashymail.com",
        "trashymail.net",
        "trbvm.com",
        "trialmail.de",
        "trillianpro.com",
        "tryalert.com",
        "turual.com",
        "twinmail.de",
        "tyldd.com",
        "uggsrock.com",
        "umail.net",
        "upliftnow.com",
        "uplipht.com",
        "uroid.com",
        "us.af",
        "venompen.com",
        "veryrealemail.com",
        "viditag.com",
        "viewcastmedia.com",
        "viewcastmedia.net",
        "viewcastmedia.org",
        "vomoto.com",
        "vubby.com",
        "walala.org",
        "walkmail.net",
        "webemail.me",
        "webm4il.info",
        "webuser.in",
        "wh4f.org",
        "whyspam.me",
        "willselfdestruct.com",
        "winemaven.info",
        "wronghead.com",
        "wuzup.net",
        "wuzupmail.net",
        "www.e4ward.com",
        "www.gishpuppy.com",
        "www.mailinator.com",
        "wwwnew.eu",
        "x.ip6.li",
        "xagloo.com",
        "xemaps.com",
        "xents.com",
        "xmaily.com",
        "xoxy.net",
        "yapped.net",
        "yeah.net",
        "yep.it",
        "yogamaven.com",
        "yopmail.com",
        "yopmail.fr",
        "yopmail.net",
        "yourdomain.com",
        "ypmail.webredirect.org",
        "yuurok.com",
        "zehnminuten.de",
        "zehnminutenmail.de",
        "zetmail.com",
        "zippymail.info",
        "zoaxe.com",
        "zoemail.org",
        "zomg.info",
    }

    # Cache keys
    CACHE_PREFIX_MX = "email_mx_"
    CACHE_PREFIX_DOMAIN = "email_domain_"
    CACHE_TIMEOUT = 3600  # 1 hour

    def __init__(self):
        self.dns_resolver = dns.resolver.Resolver()
        self.dns_resolver.timeout = 5  # 5 second timeout
        self.dns_resolver.lifetime = 10  # 10 second lifetime

    def validate_email_domain(self, email: str) -> Tuple[bool, str, Dict]:
        """
        Comprehensive email domain validation

        Args:
            email: Email address to validate

        Returns:
            Tuple of (is_valid, error_message, validation_details)
        """
        try:
            # Extract domain from email
            domain = self._extract_domain(email)
            if not domain:
                return False, "Invalid email format", {"domain": None}

            validation_details = {
                "domain": domain,
                "is_disposable": False,
                "has_mx_record": False,
                "mx_records": [],
                "validation_method": "comprehensive",
            }

            # Check if domain is disposable
            if self._is_disposable_domain(domain):
                validation_details["is_disposable"] = True
                logger.warning(
                    "Disposable email domain detected",
                    extra={
                        "event_type": "DISPOSABLE_EMAIL_BLOCKED",
                        "domain": domain,
                        "email": email,
                    },
                )
                return (
                    False,
                    "Disposable email addresses are not allowed",
                    validation_details,
                )

            # Check MX records
            mx_valid, mx_records = self._check_mx_records(domain)
            validation_details["has_mx_record"] = mx_valid
            validation_details["mx_records"] = mx_records

            if not mx_valid:
                logger.warning(
                    "Email domain has no valid MX records",
                    extra={
                        "event_type": "INVALID_MX_DOMAIN_BLOCKED",
                        "domain": domain,
                        "email": email,
                    },
                )
                return False, "Email domain cannot receive emails", validation_details

            # All validations passed
            logger.info(
                "Email domain validation successful",
                extra={
                    "event_type": "EMAIL_DOMAIN_VALIDATION_SUCCESS",
                    "domain": domain,
                    "mx_count": len(mx_records),
                },
            )

            return True, "", validation_details

        except Exception as e:
            logger.error(
                f"Email domain validation error: {str(e)}",
                extra={
                    "event_type": "EMAIL_DOMAIN_VALIDATION_ERROR",
                    "email": email,
                    "error": str(e),
                },
                exc_info=True,
            )
            # In case of validation error, allow the email (fail open)
            return True, "", {"domain": domain, "error": str(e)}

    def _extract_domain(self, email: str) -> Optional[str]:
        """Extract domain from email address"""
        try:
            if "@" not in email:
                return None
            domain = email.split("@")[1].lower().strip()
            return domain if domain else None
        except (IndexError, AttributeError):
            return None

    def _is_disposable_domain(self, domain: str) -> bool:
        """Check if domain is in disposable email list"""
        # Check exact match
        if domain in self.DISPOSABLE_DOMAINS:
            return True

        # Check subdomain patterns (e.g., subdomain.mailinator.com)
        domain_parts = domain.split(".")
        if len(domain_parts) > 2:
            # Check if parent domain is disposable
            parent_domain = ".".join(domain_parts[-2:])
            if parent_domain in self.DISPOSABLE_DOMAINS:
                return True

        return False

    def _check_mx_records(self, domain: str) -> Tuple[bool, List[str]]:
        """
        Check if domain has valid MX records

        Returns:
            Tuple of (has_valid_mx, mx_records_list)
        """
        cache_key = f"{self.CACHE_PREFIX_MX}{domain}"
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            return cached_result

        try:
            # Query MX records
            mx_records = []
            answers = self.dns_resolver.resolve(domain, "MX")

            for rdata in answers:
                mx_host = str(rdata.exchange).rstrip(".")
                mx_records.append(mx_host)

            result = (len(mx_records) > 0, mx_records)

            # Cache the result
            cache.set(cache_key, result, self.CACHE_TIMEOUT)

            logger.debug(
                f"MX record lookup successful for {domain}",
                extra={
                    "domain": domain,
                    "mx_count": len(mx_records),
                    "mx_records": mx_records[:3],  # Log first 3 for brevity
                },
            )

            return result

        except dns.resolver.NXDOMAIN:
            # Domain doesn't exist
            result = (False, [])
            cache.set(cache_key, result, self.CACHE_TIMEOUT)
            return result

        except dns.resolver.NoAnswer:
            # Domain exists but no MX records
            result = (False, [])
            cache.set(cache_key, result, self.CACHE_TIMEOUT)
            return result

        except (dns.exception.Timeout, dns.exception.DNSException) as e:
            logger.warning(
                f"DNS lookup failed for {domain}: {str(e)}",
                extra={
                    "domain": domain,
                    "error": str(e),
                    "error_type": e.__class__.__name__,
                },
            )
            # In case of DNS error, assume valid (fail open)
            return (True, [])

        except Exception as e:
            logger.error(
                f"Unexpected error during MX lookup for {domain}: {str(e)}",
                extra={
                    "domain": domain,
                    "error": str(e),
                    "error_type": e.__class__.__name__,
                },
                exc_info=True,
            )
            # In case of unexpected error, assume valid (fail open)
            return (True, [])

    def add_disposable_domain(self, domain: str) -> None:
        """Add a domain to the disposable list (for dynamic updates)"""
        self.DISPOSABLE_DOMAINS.add(domain.lower().strip())
        logger.info(
            f"Added disposable domain: {domain}",
            extra={
                "event_type": "DISPOSABLE_DOMAIN_ADDED",
                "domain": domain,
            },
        )

    def is_domain_valid(self, domain: str) -> bool:
        """Quick check if domain is valid (not disposable and has MX)"""
        if self._is_disposable_domain(domain):
            return False

        has_mx, _ = self._check_mx_records(domain)
        return has_mx


# Global instance for easy access
email_domain_validator = EmailDomainValidator()
